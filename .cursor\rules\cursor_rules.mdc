---
description: Guidelines for creating and maintaining Cursor rules to ensure consistency and effectiveness.
globs: .cursor/rules/*.mdc
alwaysApply: true
---
---
description: Guidelines for creating and maintaining Roo Code rules to ensure consistency and effectiveness within the DafnckMachine project.
globs: .cursor/rules/*.mdc
alwaysApply: true
---

Execute to follow each phase, step and task and subtask inside `01_Machine/01_Workflow` defined by [DNA.json](mdc:01_Machine/03_Brain/DNA.json) progress using [Step.json](mdc:01_Machine/03_Brain/Step.json) to maintain awareness of the current phase, step, and task. Upon completion of each task, automatically transition to the subsequent task within the current step. Upon completion of each step, automatically transition to the first step of the next phase, as dictated by [DNA.json](mdc:01_Machine/03_Brain/DNA.json) Ensure all actions are driven by the data structures within [DNA.json](mdc:01_Machine/03_Brain/DNA.json) and [Step.json](mdc:01_Machine/03_Brain/Step.json). 

- **Project Structure Alignment:**
  - Rules must align with the project's directory structure as defined in `01_Machine/01_WorkflUse globs to target relevant files anes.
  - Example: `globs: 03_Projec for TypeScript source files.

- **Agent-Specific Rules:**
  - Rules should consider the capabilities and responsibilities of different agents as defined in `01_Machine/02_Agents`.
  - Example: A rule for code formatting might be more relevant to the coding-agent than the ux-researcher-agent.
  - Reference agent configurations using `[agent-name](mdc:01_Machine/02_Agents/agent-name.json)`.

- **Workflow Integration:**
  - Rules should support the defined project workflow in `01_Machine/01_Workflow`.
  - Consider the phase and step in the workflow when creating rules.
  - Example: A rule for code review might be more relevant in the "Development & Quality Assurance" phase.

- **Brain Consistency:**
  - Rules should align with the system's core configuration defined in `01_Machine/03_Brain/Genesis.json` and `01_Machine/03_Brain/DNA.json`.
  - Ensure rules are compatible with the defined project types and agent capabilities.

- **Documentation Standards:**
  - Rules should promote clear and consistent documentation practices as outlined in `01_Machine/04_Documentation`.
  - Encourage the use of documentation templates and style guides.

- **Required Rule Structure:**
  ```markdown
  ---
  description: Clear, one-line description of what the rule enforces
  globs: path/to/files/*.ext, other/path/**/*
  alwaysApply: boolean
  ---

  - **Main Points in Bold**
    - Sub-points with details
    - Examples and explanations
  ```

- **File References:**
  - Use `[filename](mdc:path/to/file)` ([filename](mdc:filename)) to reference files
  - Example: [prisma.mdc](mdc:.cursor/rules/prisma.mdc) for rule references
  - Example: [schema.prisma](mdc:prisma/schema.prisma) for code references

- **Agent References:**
  - Use `[agent-name](mdc:01_Machine/02_Agents/agent-name.json)` to reference agent configurations.
  - Example: [coding-agent](mdc:01_Machine/02_Agents/coding-agent.json) for coding agent rules.

- **Workflow Phase References:**
  - Use `[phase-name](mdc:01_Machine/01_Workflow)` to reference workflow phases.
  - Example: [Phase 3: Product Definition & Design](mdc:01_Machine/01_Workflow) for design-related rules.

- **Code Examples:**
  - Use language-specific code blocks
  ```typescript
  // ✅ DO: Show good examples
  const goodExample = true;
  
  // ❌ DON'T: Show anti-patterns
  const badExample = false;
  ```

- **Rule Content Guidelines:**
  - Start with high-level overview
  - Include specific, actionable requirements
  - Show examples of correct implementation
  - Reference existing code when possible
  - Keep rules DRY by referencing other rules

- **Rule Maintenance:**
  - Update rules when new patterns emerge
  - Add examples from actual codebase
  - Remove outdated patterns
  - Cross-reference related rules

- **Best Practices:**
  - Use bullet points for clarity
  - Keep descriptions concise
  - Include both DO and DON'T examples
  - Reference actual code over theoretical examples
  - Use consistent formatting across rules