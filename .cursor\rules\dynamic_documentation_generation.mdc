---
description: 
globs: 
alwaysApply: false
---
## Dynamic Documentation Generation Principles

This rule outlines the standard for creating and managing all project documentation, support materials, and output artifacts within the DafnckMachine. The core principle is to generate these resources dynamically and on-demand, leveraging real-time research, continuous learning, and analysis of current best practices. This approach ensures content reflects the latest industry standards and specific project needs, moving away from outdated, pre-existing template files.

### 1. Deliverables Categories & Locations

Documentation and artifacts are categorized and stored as follows:

-   **General Documentation & Support Files:**
    -   **Content**: Comprehensive documentation, support materials, and helpful files relevant to the project's overall context.
    -   **Generation Focus**: Create only the documentation files specifically needed for the current context, task, or request.
    -   **Location**: `01_Machine/04_Documentation/Doc/Phase_[X]/[01_Step_Name]/`
        -   `[X]` corresponds to the current project phase number (e.g., `Phase_0`, `Phase_1`).
        -   `[01_Step_Name]` represents an optional, sequential subfolder for specific steps or major tasks within a phase (e.g., `01_Requirements`, `02_Design_Principles`). This ensures granular organization.
    -   **Formats**: Primarily Markdown (`.md`) and JSON (`.json`).

-   **Project-Specific Documentation & Output Artifacts:**
    -   **Content**: All documentation specific to the project's unique elements, related files, and any output artifacts generated as a result of the project's progression or current phase activity.
    -   **Generation Focus**: Dynamically create these resources using real-time research and best practice analysis. Crucially, generate only the necessary files for the current task or phase. This process will also leverage understanding and outcomes gained from all preceding phases to ensure seamless continuity and contextual relevance.
    -   **Location**: `01_Machine/04_Documentation/vision/Phase_[X]/[01_Step_Name]/`
        -   `[X]` corresponds to the relevant project phase (e.g., `Phase_1` for outputs related to Phase 1).
        -   `[01_Step_Name]` represents an optional, sequential subfolder for specific steps or major tasks within a phase (e.g., `01_Initial_Concept`, `02_Prototype_Output`). This allows for detailed organization.
    -   **Formats**: Markdown (`.md`) and JSON (`.json`) for documentation. Other appropriate formats will be used for specific artifacts as needed (e.g., code files, diagrams, reports).

### 2. Guiding Principles for Generation

All generated content must adhere to the following principles:

-   **Accuracy**: All generated content must be factually correct and technically sound.
-   **Clarity**: Present information in a clear, concise, and easily understandable manner, avoiding jargon where possible.
-   **Best Practices**: Strictly adhere to current industry best practices, coding standards, and established methodologies. Utilize real-time research and continuous learning to maintain this standard.
-   **Dynamism**: Generated content is not static. It should be continuously updated and refined based on new research, evolving best practices, and project changes. Files are generated when needed and reflect the current state of knowledge and project progress.
-   **Contextual Completeness**: Provide comprehensive coverage of all relevant topics and aspects *for the task or phase at hand*. Strictly ensure that only the documentation and artifacts required for the specific context are generated, avoiding unnecessary output or premature creation of files.
-   **Phase & Step Awareness**: The generation process for any given phase and step must deeply incorporate and build upon the knowledge, decisions, and outcomes established in all preceding phases and steps. This ensures a consistent, logical, and evolving project narrative from start to finish.
-   **No Pre-existing Templates**: Agents should not rely on or expect pre-existing template files at the target locations. All files are to be created dynamically with the necessary content at the moment they are required by the workflow.

### 3. Implementation Notes

-   Agents responsible for tasks that produce documentation (as defined in workflow files like `01_User_Briefing.md`) will use tools like `edit_file` to create these documents from scratch.
-   The content of the generated files will be derived from agent analysis, user interaction, research, and the outcomes of preceding tasks, phases, and steps.
-   This rule supersedes any previous practices that relied on static templates for the specified documentation paths.
