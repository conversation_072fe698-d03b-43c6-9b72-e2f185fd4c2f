# Development Standards and Conventions

## 1. Overview
Define coding standards, naming conventions, and best practices for frontend development in DafnckMachine v3.1.

**Example:**
- "All React components use PascalCase, and files are named after the component."

## 2. Coding Standards
- Use ES6+ JavaScript/TypeScript features
- Enforce code style with ESLint and Prettier
- Write modular, reusable code

**Example Table:**
| Standard         | Rule/Tool         | Example                        |
|-----------------|-------------------|--------------------------------|
| Component Naming| PascalCase        | MyComponent.tsx                |
| File Naming     | PascalCase        | MyComponent.tsx                |
| Linting         | ESLint, Prettier  | Consistent formatting          |

## 3. Naming Conventions
- Components: PascalCase (e.g., UserCard)
- Variables: camelCase (e.g., userName)
- Constants: UPPER_SNAKE_CASE (e.g., API_URL)

## 4. Best Practices
- Keep components small and focused
- Use prop types or TypeScript interfaces
- Document components with JSDoc or similar
- Avoid inline styles; use CSS modules or styled-components

## 5. Code Review Process
- All code changes require peer review
- Use pull requests with clear descriptions
- Address all review comments before merging

## 6. Success Criteria
- Codebase is consistent, readable, and maintainable
- All team members follow the same standards

## 7. Validation Checklist
- [ ] Coding standards are defined
- [ ] Naming conventions are specified
- [ ] Best practices are documented
- [ ] Code review process is described

---
*This document follows the DafnckMachine v3.1 PRD template. Update as development standards or conventions evolve.* 