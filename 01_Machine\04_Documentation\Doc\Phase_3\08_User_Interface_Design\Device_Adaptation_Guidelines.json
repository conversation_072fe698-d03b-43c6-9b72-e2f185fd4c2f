{"devices": {"mobile": {"adaptation": "Stack content vertically, use large touch targets, hide non-essential elements."}, "tablet": {"adaptation": "Flexible grid, support both orientations, show side navigation if space allows."}, "desktop": {"adaptation": "Multi-column layouts, hover states, keyboard navigation."}, "largeScreen": {"adaptation": "Increase spacing, use larger UI elements, optimize for distance viewing."}}, "validationChecklist": ["Adaptation strategy is defined for each device type", "Guidelines cover all supported devices"]}