{"metadata": {"version": "3.1.0", "last_updated": "2025-01-06T22:06:00.000Z"}, "workflow_state": {"current_step": "P02-S02-T06-Context-and-Market-Analysis", "current_phase": "phase_2", "current_task": "P02-S02-T06-Context-and-Market-Analysis", "previous_step": "P02-S02-T05-Technical-Feasibility-Assessment", "next_step": "P02-S02-T07-Business-Viability-Analysis", "progress": {"total_steps": 7, "completed_steps": 5, "current_step_number": 6, "percentage": 71, "phase_1_completed": true, "phase_2_started": true}, "session": {"started_at": "2025-01-27T10:00:00.000Z", "last_updated": "2025-01-27T12:45:00.000Z", "status": "phase_2_in_progress", "project_type": "transport_ecosystem"}}, "agents": {"uber-orchestrator-agent": {"id": "ORCH-001", "name": "Uber Orchestrator Agent", "capabilities": ["workflow_coordination", "state_management", "task_delegation", "progress_tracking"], "persona": "system_orchestrator", "phases": ["all"], "workflow_steps": ["all"]}, "nlu-processor-agent": {"id": "NLU-001", "name": "NLU Processor Agent", "capabilities": ["user_profiling", "requirement_extraction", "natural_language_processing"], "persona": "expert_analyst", "phases": ["phase_1"], "workflow_steps": ["P01-S01-T01-User-Profile-Development"]}, "elicitation-agent": {"id": "ELICIT-001", "name": "Requirements Elicitation Agent", "capabilities": ["requirement_extraction", "user_interview", "stakeholder_mapping", "requirements_analysis"], "persona": "expert_business_analyst", "phases": ["phase_1"], "workflow_steps": ["P01-S01-T02-Project-Vision-Elicitation", "P01-S01-T03-Success-Criteria-Definition", "P01-S01-T04-Requirement-Analysis", "P01-S01-T05-Technical-Constraints"]}}, "workflow_progression": {"step_sequence": ["P01-S01-T01-User-Profile-Development", "P01-S01-T02-Project-Vision-Elicitation", "P01-S01-T03-Success-Criteria-Definition", "P01-S01-T04-Requirement-Analysis", "P01-S01-T05-Technical-Constraints"], "auto_progression": false, "require_completion_confirmation": true}, "step_definitions": {"P01-S01-T01-User-Profile-Development": {"agent": "nlu-processor-agent", "phase": "phase_1", "task_id": "P01-S01-T01", "file_path": "01_Machine/01_Workflow/Phase 1: Initial User Input & Project Inception/P01-S01-T01-User-Profile-Development.md", "estimated_duration_minutes": 30, "previous_task": null, "next_task": "P01-S01-T02-Project-Vision-Elicitation"}, "P01-S01-T02-Project-Vision-Elicitation": {"agent": "elicitation-agent", "phase": "phase_1", "task_id": "P01-S01-T02", "file_path": "01_Machine/01_Workflow/Phase 1: Initial User Input & Project Inception/P01-S01-T02-Project-Vision-Elicitation.md", "estimated_duration_minutes": 45, "previous_task": "P01-S01-T01-User-Profile-Development", "next_task": "P01-S01-T03-Success-Criteria-Definition"}, "P01-S01-T03-Success-Criteria-Definition": {"agent": "elicitation-agent", "phase": "phase_1", "task_id": "P01-S01-T03", "file_path": "01_Machine/01_Workflow/Phase 1: Initial User Input & Project Inception/P01-S01-T03-Success-Criteria-Definition.md", "estimated_duration_minutes": 30, "previous_task": "P01-S01-T02-Project-Vision-Elicitation", "next_task": "P01-S01-T04-Requirement-Analysis"}, "P01-S01-T04-Requirement-Analysis": {"agent": "elicitation-agent", "phase": "phase_1", "task_id": "P01-S01-T04", "file_path": "01_Machine/01_Workflow/Phase 1: Initial User Input & Project Inception/P01-S01-T04-Requirement-Analysis.md", "estimated_duration_minutes": 60, "previous_task": "P01-S01-T03-Success-Criteria-Definition", "next_task": "P01-S01-T05-Technical-Constraints"}, "P01-S01-T05-Technical-Constraints": {"agent": "elicitation-agent", "phase": "phase_1", "task_id": "P01-S01-T05", "file_path": "01_Machine/01_Workflow/Phase 1: Initial User Input & Project Inception/P01-S01-T05-Technical-Constraints.md", "estimated_duration_minutes": 30, "previous_task": "P01-S01-T04-Requirement-Analysis", "next_task": null}}, "tracking_functions": {"start_workflow": {"description": "Initialize workflow tracking for a new project", "updates": ["workflow_state.session", "workflow_state.current_step", "workflow_state.progress"], "sets_current_step": "P01-S01-T01-User-Profile-Development"}, "complete_step": {"description": "Mark current step as complete and move to next", "updates": ["workflow_state.previous_step", "workflow_state.current_step", "workflow_state.next_step", "workflow_state.progress"], "auto_progression": false}, "get_current_status": {"description": "Get current workflow position and progress", "returns": ["current_step", "previous_step", "next_step", "progress_percentage", "current_agent"]}, "update_progress": {"description": "Update progress within current step", "updates": ["workflow_state.current_task", "workflow_state.progress"]}}, "agent_instructions": {"start_command": "When user says 'Let's get started', call start_workflow() and begin with step P01-S01-T01-User-Profile-Development", "step_completion": "When step is complete, call complete_step() to move to next step with confirmation", "status_check": "Use get_current_status() to always know where you are in the workflow", "file_reference": "Always reference the current step's file_path for detailed instructions"}}