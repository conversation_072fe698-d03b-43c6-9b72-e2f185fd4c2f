# Agents RooCode pour Cursor

Ce projet utilise des agents spécialisés convertis depuis RooCode pour améliorer l'expérience de développement dans Cursor.

## Utilisation rapide

Utilisez `@nom-de-l-agent` suivi de votre demande pour activer un agent spécifique :

```
@uber-orchestrator-agent Aide-moi à planifier ce projet complexe
@coding-agent Optimise cette fonction pour de meilleures performances
@scribe-agent Documente ces changements
```

## Agents principaux

### 🎩 Orchestration et Gestion
- `@uber-orchestrator-agent` - Orchestrateur principal pour projets complexes
- `@task-planning-agent` - Planification et gestion des tâches
- `@project-initiator-agent` - Initiation et configuration de projets

### 💡 Idéation et Recherche
- `@idea-generation-agent` - Génération d'idées créatives
- `@idea-refinement-agent` - Raffinement et amélioration d'idées
- `@market-research-agent` - Recherche de marché et analyse concurrentielle
- `@deep-research-agent` - Recherche approfondie multi-domaines

### 🏗️ Architecture et Développement
- `@system-architect-agent` - Architecture système et technique
- `@coding-agent` - Développement et programmation
- `@code-reviewer-agent` - Révision et amélioration du code
- `@tech-spec-agent` - Spécifications techniques

### 🎨 Design et UX
- `@ui-designer-agent` - Design d'interface utilisateur
- `@ux-researcher-agent` - Recherche utilisateur et UX
- `@design-system-agent` - Systèmes de design
- `@prototyping-agent` - Prototypage et maquettes

### 🧪 Tests et Qualité
- `@test-orchestrator-agent` - Orchestration des tests
- `@functional-tester-agent` - Tests fonctionnels
- `@performance-load-tester-agent` - Tests de performance
- `@security-auditor-agent` - Audit de sécurité

### 📊 Analytics et Marketing
- `@analytics-setup-agent` - Configuration d'analytics
- `@marketing-strategy-orchestrator` - Stratégie marketing
- `@seo-sem-agent` - SEO et marketing digital
- `@content-strategy-agent` - Stratégie de contenu

### 📝 Documentation et Communication
- `@scribe-agent` - Documentation et prise de notes
- `@documentation-agent` - Documentation technique
- `@elicitation-agent` - Collecte des exigences

### 🔧 DevOps et Déploiement
- `@devops-agent` - DevOps et infrastructure
- `@adaptive-deployment-strategist-agent` - Stratégies de déploiement
- `@health-monitor-agent` - Monitoring et santé système

## Conseils d'utilisation

1. **Soyez spécifique** : Plus votre demande est précise, meilleure sera la réponse
2. **Utilisez le bon agent** : Chaque agent a sa spécialité
3. **Combinez les agents** : N'hésitez pas à mentionner plusieurs agents
4. **Fournissez du contexte** : Donnez suffisamment d'informations sur votre situation

## Exemples d'utilisation

### Planification de projet

@uber-orchestrator-agent Je veux créer une application de gestion de tâches. 
Peux-tu m'aider à planifier le projet et identifier les agents nécessaires ?


### Développement

@coding-agent Crée une fonction React pour afficher une liste de tâches 


### Design
```
@ui-designer-agent Conçois une interface moderne pour une application 
de gestion de tâches, avec un design system cohérent
```

### Tests
```
@test-orchestrator-agent Crée une stratégie de test complète pour 
une application web avec authentification et base de données
```

Pour plus d'informations, consultez le guide complet dans `cursor_config/CURSOR_SETUP_GUIDE.md`.
