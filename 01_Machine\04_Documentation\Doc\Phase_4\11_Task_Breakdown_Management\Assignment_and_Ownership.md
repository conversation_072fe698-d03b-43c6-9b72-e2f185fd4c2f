# Assignment and Ownership

## 1. Overview
Describe guidelines for assigning tasks, establishing ownership, and ensuring accountability in DafnckMachine v3.1.

**Example:**
- "Each task is assigned to a single owner responsible for delivery and status updates."

## 2. Assignment Guidelines
- Assign tasks based on expertise, workload, and availability
- Ensure each task has a clear owner
- Allow for reassignment as priorities or team composition change

**Example Table:**
| Task                | Owner      | Backup      | Assignment Date | Status   |
|---------------------|------------|-------------|----------------|----------|
| Implement login API | Alice      | Bob         | 2024-06-10     | Pending  |
| Create login UI     | Bob        | Carol       | 2024-06-10     | In Progress |

## 3. Ownership Responsibilities
- Owners are responsible for task delivery, updates, and communication
- Owners must flag blockers or risks early
- Owners ensure task meets acceptance criteria before completion

## 4. Accountability Practices
- Regular check-ins and standups
- Use of task tracking tools for visibility
- Recognition of ownership in documentation and reviews

## 5. Success Criteria
- All tasks have clear owners
- Accountability is maintained throughout the process

## 6. Validation Checklist
- [ ] Assignment guidelines are described
- [ ] Example assignment table is included
- [ ] Ownership responsibilities are specified
- [ ] Accountability practices are documented

---
*This document follows the DafnckMachine v3.1 PRD template. Update as assignment or ownership practices evolve.* 