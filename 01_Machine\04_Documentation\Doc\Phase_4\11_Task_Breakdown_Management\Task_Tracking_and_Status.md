# Task Tracking and Status

## 1. Overview
Describe methods and tools for tracking task progress and status in DafnckMachine v3.1.

**Example:**
- "All tasks are tracked in a Kanban board with status columns for Pending, In Progress, Review, and Done."

## 2. Tracking Methods
- Use Kanban boards (physical or digital)
- Update status at each workflow stage
- Link tasks to user stories, features, or epics

**Example Table:**
| Task                | Status       | Last Updated | Notes                |
|---------------------|-------------|--------------|----------------------|
| Implement login API | In Progress | 2024-06-12   | Blocked by API spec  |
| Create login UI     | Review      | 2024-06-13   | Awaiting feedback    |

## 3. Status Definitions
- Pending: Task is ready to be started
- In Progress: Work is actively being done
- Review: Task is complete and under review
- Done: Task is complete and accepted

## 4. Reporting & Visibility
- Regular status updates in standups or reports
- Use dashboards for real-time visibility
- Archive completed tasks for future reference

## 5. Success Criteria
- All tasks are tracked and status is up to date
- Team has visibility into progress and blockers

## 6. Validation Checklist
- [ ] Tracking methods are described
- [ ] Status definitions are included
- [ ] Example tracking table is present
- [ ] Reporting and visibility practices are documented

---
*This document follows the DafnckMachine v3.1 PRD template. Update as tracking tools or status practices evolve.* 