# End-to-End Testing Guidelines

## 1. Overview
Describe E2E testing strategies, tools, and workflows for DafnckMachine v3.1.

**Example:**
- "E2E tests simulate real user flows using Cypress."

## 2. Tools and Frameworks
- List recommended tools (e.g., <PERSON><PERSON>, Playwright, Selenium).

| Tool       | Purpose           |
|------------|------------------|
| Cypress    | E2E browser tests |
| Playwright | Cross-browser E2E|

## 3. E2E Test Workflows
- Simulate user journeys across the system
- Test critical paths (login, task creation, etc.)

**Example Workflow:**
- "User signs up, logs in, and creates a new task."

## 4. Environment Setup
- Use dedicated E2E environments
- Reset state before each test run

## 5. Success Criteria
- All critical user flows are tested
- E2E tests are stable and repeatable

## 6. Validation Checklist
- [ ] Tools and frameworks are listed
- [ ] E2E workflows are described
- [ ] Example workflow is included
- [ ] Environment setup is specified
- [ ] Success criteria are documented

---
*This document follows the DafnckMachine v3.1 PRD template. Update as E2E testing practices evolve.* 