{"requirements_matrix": {"metadata": {"version": "1.0.0", "created_at": "2025-01-27T10:45:00Z", "agent_responsible": "elicitation-agent", "task_id": "P01-S01-T04-Requirement-Analysis", "total_requirements": 25, "applications_covered": 6}, "functional_requirements": [{"id": "FR-CTRL-001", "title": "Gestion des Données Maîtres", "description": "Système centralisé pour gérer toutes les données de référence (itinéraires, horaires, flotte, tarifs, agences, utilisateurs)", "application": "Control", "priority": "Critical", "complexity": "High", "status": "Defined", "dependencies": ["Database Architecture"], "acceptance_criteria": "AC-CTRL-001", "estimated_effort": "40 hours", "business_value": "High"}, {"id": "FR-CTRL-002", "title": "Authentification et Permissions", "description": "Système d'authentification unique avec gestion fine des permissions (SSO, RBAC, gestion des rôles par application)", "application": "Control", "priority": "Critical", "complexity": "High", "status": "Defined", "dependencies": ["Central Auth Service"], "acceptance_criteria": "AC-CTRL-002", "estimated_effort": "32 hours", "business_value": "High"}, {"id": "FR-CTRL-003", "title": "Gestion de la Flotte et Maintenance (FMS)", "description": "Système complet de gestion de maintenance préventive et corrective (carnet numérique, alertes, planification, stock pièces)", "application": "Control", "priority": "High", "complexity": "High", "status": "Defined", "dependencies": ["FR-CTRL-001"], "acceptance_criteria": "AC-CTRL-004", "estimated_effort": "60 hours", "business_value": "High"}, {"id": "FR-WEB-001", "title": "Réservation Temps Réel", "description": "Système de réservation avec sélection de sièges synchronisée (disponibilité temps réel, prévention double réservation)", "application": "Site Web", "priority": "Critical", "complexity": "High", "status": "Defined", "dependencies": ["FR-GUI-002", "Real-time Sync Service"], "acceptance_criteria": "AC-WEB-001", "estimated_effort": "48 hours", "business_value": "Critical"}, {"id": "FR-WEB-002", "title": "Paiements Mobiles Intégrés", "description": "Intégration complète des solutions de paiement mobile (Orange Money, MTN, Moov, Wave, gestion multi-devises)", "application": "Site Web", "priority": "Critical", "complexity": "High", "status": "Defined", "dependencies": ["Payment Partner APIs"], "acceptance_criteria": "AC-WEB-002", "estimated_effort": "56 hours", "business_value": "Critical"}, {"id": "FR-WEB-003", "title": "Billets Électroniques", "description": "Génération et gestion de billets électroniques sécurisés (QR codes uniques, validation, envoi email/SMS)", "application": "Site Web", "priority": "Critical", "complexity": "Medium", "status": "Defined", "dependencies": ["Notification Service"], "acceptance_criteria": "AC-WEB-004", "estimated_effort": "24 hours", "business_value": "High"}, {"id": "FR-GUI-001", "title": "Mode Hors-ligne <PERSON>", "description": "Fonctionnement complet sans connexion internet (synchronisation différée, file d'attente, résolution conflits)", "application": "<PERSON><PERSON><PERSON>", "priority": "Critical", "complexity": "High", "status": "Defined", "dependencies": ["Local Database", "Sync Service"], "acceptance_criteria": "AC-GUI-001", "estimated_effort": "72 hours", "business_value": "Critical"}, {"id": "FR-GUI-002", "title": "Synchronisation Multi-terminaux", "description": "Cohérence des données entre plusieurs postes de guichet (temps réel, gestion concurrence, verrouillage optimiste)", "application": "<PERSON><PERSON><PERSON>", "priority": "Critical", "complexity": "High", "status": "Defined", "dependencies": ["Real-time Sync Service"], "acceptance_criteria": "AC-GUI-002", "estimated_effort": "40 hours", "business_value": "Critical"}, {"id": "FR-GUI-003", "title": "Interface de Vente Rapide", "description": "Interface optimisée pour transactions rapides (workflow simplifié, racco<PERSON><PERSON> clavier, impression locale)", "application": "<PERSON><PERSON><PERSON>", "priority": "High", "complexity": "Medium", "status": "Defined", "dependencies": ["FR-GUI-001"], "acceptance_criteria": "AC-GUI-003", "estimated_effort": "32 hours", "business_value": "High"}, {"id": "FR-COU-001", "title": "Cycle de Vie Complet des Colis", "description": "Gestion complète de l'enregistrement à la livraison (statuts, traçabilité, notifications automatiques)", "application": "<PERSON><PERSON><PERSON>", "priority": "Critical", "complexity": "High", "status": "Defined", "dependencies": ["Notification Service", "FR-MOB-003"], "acceptance_criteria": "AC-COU-001", "estimated_effort": "48 hours", "business_value": "High"}, {"id": "FR-COU-002", "title": "Traçabilité Avancée avec QR Codes", "description": "Système de traçabilité avec codes QR uniques (génération, scan, mise à jour statuts)", "application": "<PERSON><PERSON><PERSON>", "priority": "Critical", "complexity": "Medium", "status": "Defined", "dependencies": ["FR-MOB-002"], "acceptance_criteria": "AC-COU-004", "estimated_effort": "24 hours", "business_value": "High"}, {"id": "FR-FIN-001", "title": "Consolidation Automatique des Revenus", "description": "Agrégation automatique de toutes les sources de revenus (billetterie, bagages, colis, temps réel)", "application": "Finance", "priority": "Critical", "complexity": "High", "status": "Defined", "dependencies": ["All Business Applications"], "acceptance_criteria": "AC-FIN-001", "estimated_effort": "40 hours", "business_value": "Critical"}, {"id": "FR-FIN-002", "title": "Analyse de Rentabilité Multi-dimensionnelle", "description": "Calculs de rentabilité par ligne, véhicule, période (KPIs automatiques, comparaisons, tendances)", "application": "Finance", "priority": "High", "complexity": "High", "status": "Defined", "dependencies": ["FR-FIN-001", "FR-CTRL-001"], "acceptance_criteria": "AC-FIN-003", "estimated_effort": "48 hours", "business_value": "High"}, {"id": "FR-MOB-001", "title": "Authentification Mobile Sécurisée", "description": "Authentification sécurisée pour agents terrain (biométrie, tokens, mode hors-ligne)", "application": "Mobile Agent", "priority": "Critical", "complexity": "Medium", "status": "Future", "dependencies": ["FR-CTRL-002"], "acceptance_criteria": "TBD", "estimated_effort": "24 hours", "business_value": "Medium"}, {"id": "FR-MOB-002", "title": "Scan QR et Validation", "description": "Scan de QR codes pour billets, bagages, colis (validation temps réel, mode hors-ligne)", "application": "Mobile Agent", "priority": "Critical", "complexity": "Medium", "status": "Future", "dependencies": ["FR-WEB-003", "FR-COU-002"], "acceptance_criteria": "TBD", "estimated_effort": "32 hours", "business_value": "High"}], "non_functional_requirements": [{"id": "NFR-PERF-001", "title": "Temps de Réponse", "description": "< 2 secondes pour 95% des requêtes", "category": "Performance", "priority": "High", "measurement": "APM monitoring continu", "target_value": "< 2s", "current_value": "TBD", "test_method": "Load testing"}, {"id": "NFR-PERF-003", "title": "Latence Synchronisation", "description": "< 1 seconde pour synchronisation sièges", "category": "Performance", "priority": "Critical", "measurement": "Monitoring temps réel", "target_value": "< 1s", "current_value": "TBD", "test_method": "Real-time sync testing"}, {"id": "NFR-AVAIL-001", "title": "Uptime Système", "description": "≥ 99.5% disponibilité mensuelle", "category": "Availability", "priority": "Critical", "measurement": "Monitoring 24/7", "target_value": "≥ 99.5%", "current_value": "TBD", "test_method": "Availability monitoring"}, {"id": "NFR-SEC-001", "title": "Cryptage des Données", "description": "TLS 1.3 pour toutes communications", "category": "Security", "priority": "Critical", "measurement": "Audits de sécurité", "target_value": "TLS 1.3", "current_value": "TBD", "test_method": "Security audits"}, {"id": "NFR-SCAL-001", "title": "Scalabilité Horizontale", "description": "Architecture permettant scaling horizontal", "category": "Scalability", "priority": "High", "measurement": "Tests de montée en charge", "target_value": "Linear scaling", "current_value": "TBD", "test_method": "Load testing"}], "integration_requirements": [{"id": "INT-INT-001", "title": "API Gateway Centralisée", "description": "Point d'entrée unique pour toutes les communications", "type": "Internal", "protocol": "REST/GraphQL via HTTPS", "security": "JWT, rate limiting, monitoring", "priority": "Critical"}, {"id": "INT-INT-002", "title": "Service de Synchronisation Temps Réel", "description": "WebSockets pour synchronisation critique", "type": "Internal", "protocol": "WebSocket", "technology": "Socket.io avec Redis Pub/Sub", "priority": "Critical"}, {"id": "INT-EXT-001", "title": "Paiements Mobiles", "description": "Intégration Orange Money, MTN, <PERSON><PERSON>, Wave", "type": "External", "protocol": "REST APIs sécurisées", "requirements": "Webhook callbacks, réconciliation", "priority": "Critical"}, {"id": "INT-EXT-002", "title": "Services de Notification", "description": "SMS, Email, WhatsApp (optionnel)", "type": "External", "protocol": "REST APIs", "providers": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ou équivalents locaux", "priority": "High"}], "requirements_summary": {"total_functional": 15, "total_non_functional": 5, "total_integration": 4, "priority_breakdown": {"critical": 12, "high": 8, "medium": 4, "low": 0}, "complexity_breakdown": {"high": 10, "medium": 8, "low": 6}, "estimated_total_effort": "640 hours", "applications_coverage": {"Control": 3, "Site Web": 3, "Guichet": 3, "Courrier": 2, "Finance": 2, "Mobile Agent": 2}}, "traceability_matrix": {"vision_alignment": {"integration_poussee": ["FR-CTRL-001", "FR-WEB-001", "FR-GUI-002"], "donnees_temps_reel": ["FR-WEB-001", "FR-GUI-002", "FR-FIN-001"], "experience_utilisateur": ["FR-WEB-003", "FR-GUI-003", "FR-COU-001"], "securite_avancee": ["FR-CTRL-002", "NFR-SEC-001", "FR-MOB-001"], "adaptabilite_locale": ["FR-GUI-001", "FR-WEB-002", "INT-EXT-001"]}, "success_criteria_mapping": {"TECH-001": ["NFR-PERF-001"], "TECH-003": ["NFR-PERF-003"], "OPS-001": ["FR-GUI-003", "NFR-USA-003"], "BIZ-001": ["FR-WEB-002", "FR-FIN-001"], "BIZ-003": ["FR-WEB-003", "FR-COU-001"]}}, "risk_assessment": {"high_risk": [{"requirement": "FR-GUI-001", "risk": "Complexité synchronisation mode hors-ligne", "mitigation": "Prototypage précoce, tests déconnexion"}, {"requirement": "FR-WEB-001", "risk": "Gestion concurrence temps réel", "mitigation": "Architecture event-sourcing, tests charge"}, {"requirement": "FR-WEB-002", "risk": "Dépendance partenaires externes", "mitigation": "Contrats SLA, fallbacks, tests intégration"}], "medium_risk": [{"requirement": "FR-CTRL-003", "risk": "Complexité métier FMS", "mitigation": "Expertise domaine, validation utilisateur"}, {"requirement": "FR-FIN-002", "risk": "Calculs complexes rentabilité", "mitigation": "Validation comptable, tests unitaires"}]}}}