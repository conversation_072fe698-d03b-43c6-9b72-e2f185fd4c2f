---
title: Elicitation Techniques Guide
version: 1.0
status: new
---

# Elicitation Techniques Guide

## 1. Introduction to Project Vision Elicitation
Project Vision Elicitation is the foundational process of defining and understanding the core purpose, goals, and direction of a project. It involves collaborative discovery with stakeholders to articulate what the project aims to achieve, for whom, and what makes it unique. A clear vision serves as a guiding star for all subsequent design, development, and decision-making processes.

This guide provides an overview of common techniques to effectively elicit and define the various components of a robust project vision.

## 2. Techniques for Core Concept Discovery
Uncovering the fundamental idea and essence of the project.

*   **The "5 Whys"**: An iterative interrogative technique used to explore the cause-and-effect relationships underlying a particular problem or goal. By repeatedly asking "Why?" (five is a rule of thumb), you can peel away layers of symptoms to get to the root cause or core motivation.
    *   *Example*: "We need a new app." Why? -> "To improve customer engagement." Why? -> ...
*   **Brainstorming Sessions**: A group creativity technique designed to generate a large number of ideas for the solution of a problem. Encourage free thinking and defer judgment.
    *   *Best Practices*: Diverse group, clear problem statement, facilitator, visual recording of ideas.
*   **Mind Mapping**: A visual thinking tool that helps structure information, helping you to better analyze, comprehend, synthesize, recall and generate new ideas. Start with the central project idea and branch out with related concepts, objectives, features, etc.
*   **SWOT Analysis (Strengths, Weaknesses, Opportunities, Threats)**: While often used for business strategy, a simplified SWOT can help frame the project's core concept in its operational environment.

## 3. Defining Main Objectives
Clearly stating what the project intends to achieve.

*   **SMART Goals**: Objectives should be:
    *   **S**pecific: Clearly defined and unambiguous.
    *   **M**easurable: Quantifiable or at least providing indicators of progress.
    *   **A**chievable: Realistic given resources and constraints.
    *   **R**elevant: Aligned with broader business goals and user needs.
    *   **T**ime-bound: Having a deadline or timeframe.
*   **Aligning Objectives**: Ensure objectives directly address identified user needs and contribute to overall business or strategic goals. Differentiate between primary and secondary objectives.

## 4. Identifying Target Audience
Understanding who the project is for.

*   **User Personas (Overview)**: Semi-fictional representations of your ideal users, based on research and data. Personas typically include:
    *   Name, demographics, and a photo/icon.
    *   Goals and motivations related to the project.
    *   Needs, pain points, and challenges.
    *   Technical proficiency and context of use.
*   **Audience Segmentation**: If applicable, divide the broader audience into distinct groups with specific needs.
*   **Empathy Mapping**: A tool to visualize user attitudes and behaviors, helping to gain a deeper understanding of their experience.

## 5. Detailing Key Features
Defining the core functionalities of the project.

*   **MoSCoW Prioritization**: A technique to categorize features:
    *   **M**ust have: Critical for the project's success; non-negotiable.
    *   **S**hould have: Important but not vital; a workaround might exist.
    *   **C**ould have: Desirable but not necessary; can be included if time/resources permit.
    *   **W**on't have (this time): Explicitly out of scope for the current iteration.
*   **User Stories**: Short, simple descriptions of a feature told from the perspective of the person who desires the new capability, usually a user or customer (e.g., "As a [type of user], I want [an action] so that [a benefit].").
*   **Focus on Core Value**: Prioritize features that directly deliver the unique value proposition and solve key user problems.

## 6. Articulating Unique Value Proposition (UVP)
Explaining what makes the project stand out and why users should choose it.

*   **Key Elements of a UVP**:
    *   Clearly states the benefit offered.
    *   Identifies the target customer.
    *   Explains how it solves the customer's problem or improves their situation.
    *   Differentiates from competitors.
*   **UVP Statement Formulas**:
    *   *Example*: "For [target audience] who [need/want X], our [project/product] is a [category] that [provides Y benefit] unlike [competitors]."
*   **Focus on Benefits, Not Just Features**: Translate features into tangible benefits for the user.

## 7. Identifying Competitive Advantages
Understanding what gives the project an edge over alternatives.

*   **Competitor Analysis**: Identify direct and indirect competitors. Analyze their strengths, weaknesses, products, and market positioning.
*   **Highlighting Differentiators**: What does your project do better, faster, cheaper, or differently in a valuable way? These could be technology, unique features, user experience, business model, etc.

## 8. Tips for Documenting the Vision
Ensuring the elicited vision is clearly and effectively recorded.

*   **Be Clear and Concise**: Use simple language. Avoid jargon where possible.
*   **Use Visuals**: Diagrams, mind maps, or mockups can supplement textual descriptions.
*   **Make it Accessible**: Store the vision document where all team members and stakeholders can easily find and refer to it (e.g., `Project_Vision_Statement.md`).
*   **Iterate and Validate**: The vision isn't set in stone. Review and refine it with stakeholders as understanding evolves, especially in early stages.
*   **Ensure Consistency**: All elements of the vision (objectives, audience, features, UVP) should align and tell a coherent story. 