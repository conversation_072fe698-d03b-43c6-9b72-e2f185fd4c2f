# Infrastructure as Code Guidelines

## 1. Overview
Describe standards and tools for infrastructure automation in DafnckMachine v3.1.

**Example:**
- "Infrastructure is managed as code using Terraform and Ansible for repeatable, versioned deployments."

## 2. IaC Tools and Frameworks
- List all tools and their purposes (e.g., Terraform, Ansible, Pulumi).

| Tool      | Purpose                |
|-----------|------------------------|
| Terraform | Provision cloud infra  |
| Ansible   | Configuration mgmt     |
| Pulumi    | Infra as code (alt)    |

## 3. Best Practices
- Version control all infrastructure code
- Use modules and reusable components
- Document all resources and variables

## 4. Example Configurations
- Reference example Terraform/Ansible files or modules

## 5. Success Criteria
- Infrastructure is reproducible and versioned
- All resources are documented and automated

## 6. Validation Checklist
- [ ] IaC tools and frameworks are listed
- [ ] Best practices are described
- [ ] Example configs are referenced
- [ ] Success criteria are included

---
*This document follows the DafnckMachine v3.1 PRD template. Update as IaC practices evolve.* 