# Universal Technology Stack Support Matrix

## 1. Mission Statement
Ensure DafnckMachine v3.1 supports all major technology stacks for web, mobile, desktop, system, game, data science, blockchain, and enterprise projects.

**Example:**
- "Provide universal technology support with platform-specific agent specializations."

## 2. Supported Platforms & Technologies
List all supported platforms and their associated technologies/frameworks.

**Example Table:**
| Platform      | Languages/Frameworks                | Agent Specialization         |
|--------------|-------------------------------------|-----------------------------|
| Web          | JavaScript, React, Next.js, Node.js | Web Dev Agent               |
| Mobile       | Swift, Kotlin, React Native, Flutter| Mobile Dev Agent            |
| Desktop      | Electron, .NET, JavaFX              | Desktop Dev Agent           |
| System       | C, Rust, Go                         | System Programming Agent    |
| Data Science | Python, R, TensorFlow, PyTorch      | Data Science Agent          |
| Blockchain   | Solidity, Rust, Web3.js             | Blockchain Agent            |
| Enterprise   | Java, .NET, SAP, Oracle             | Enterprise Agent            |

## 3. Technology Matrix
Provide a matrix mapping project types to supported stacks and agent roles.

**Example:**
- "A data science project will automatically assign a Data Science Agent and recommend Python/TensorFlow."

## 4. Platform-Specific Adaptations
Describe how the system adapts to platform-specific requirements.
- Specialized agent assignment
- Technology recommendations
- Automated configuration

**Example:**
- "For mobile projects, the system configures build pipelines for iOS and Android."

## 5. Success Criteria
- All major platforms and technologies are supported
- Platform-specific agent specializations are defined
- Technology matrix is actionable and up to date

## 6. Validation Checklist
- [ ] All major platforms are listed
- [ ] Technology matrix is complete
- [ ] Agent specializations are mapped
- [ ] Platform-specific adaptations are described

---
*This document follows the DafnckMachine v3.1 PRD template. Update as new platforms or technologies are supported.* 