# Retrospectives and Lessons Learned

## 1. Overview
Describe retrospective processes, documentation of lessons learned, and action tracking for DafnckMachine v3.1.

**Example:**
- "Sprint retrospectives are held biweekly to reflect on successes, challenges, and opportunities for improvement."

## 2. Retrospective Formats
- List formats used (e.g., Start/Stop/Continue, 4Ls, Mad/Sad/Glad)

| Format              | Description                        |
|---------------------|------------------------------------|
| Start/Stop/Continue | What to start, stop, and continue  |
| 4Ls                 | Liked, Learned, Lacked, Longed for |
| Mad/Sad/Glad        | Emotional reflection               |

## 3. Documentation Practices
- Record key takeaways, action items, and owners
- Store retro notes in a shared, accessible location

## 4. Action Tracking
- Track progress on action items from retrospectives
- Review previous actions at the start of each retro

## 5. Success Criteria
- Retrospectives are regular, inclusive, and actionable
- Lessons learned are documented and drive improvement

## 6. Validation Checklist
- [ ] Retro formats are listed and described
- [ ] Documentation practices are specified
- [ ] Action tracking is described
- [ ] Success criteria are included

---
*This document follows the DafnckMachine v3.1 PRD template. Update as retro practices evolve.* 