# Typography System

## 1. Overview
Define the typography system for DafnckMachine v3.1, including font families, weights, sizes, and usage rules to ensure consistency and readability.

**Example:**
- "Use 'Inter' as the primary font for all UI text."

## 2. Font Families
- Primary: Inter, Arial, sans-serif
- Secondary: Roboto Mono, monospace

**Example Table:**
| Role      | Font Family                |
|-----------|---------------------------|
| Primary   | Inter, Arial, sans-serif  |
| Secondary | Roboto Mono, monospace    |

## 3. Font Weights & Styles
- Regular (400)
- Medium (500)
- Bold (700)

**Example:**
- "Headings use Bold, body text uses Regular."

## 4. Font Sizes & Scale
- XS: 12px
- S: 14px
- M: 16px
- L: 20px
- XL: 24px
- XXL: 32px

**Example Table:**
| Size | Usage         | px   |
|------|--------------|------|
| XS   | Captions     | 12px |
| S    | Labels       | 14px |
| M    | Body         | 16px |
| L    | Subheading   | 20px |
| XL   | Heading      | 24px |
| XXL  | Display      | 32px |

## 5. Usage Guidelines
- Use consistent line height (e.g., 1.5)
- Avoid more than two font families per screen
- Maintain sufficient contrast for accessibility

## 6. Success Criteria
- Typography is readable and consistent
- All text elements use defined styles
- System supports responsive scaling

## 7. Validation Checklist
- [ ] Font families are specified
- [ ] Font weights and sizes are documented
- [ ] Usage guidelines are clear
- [ ] Accessibility requirements are met

---
*This document follows the DafnckMachine v3.1 PRD template. Update as new fonts or typographic rules are introduced.* 